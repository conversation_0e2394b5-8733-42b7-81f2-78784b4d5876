# generate_data.py
import csv
import random

from faker import Faker
fake = Faker()

# Define the number of rows to generate
num_rows = 50000
# Define the file name
file_name = "large_dataset.csv"
# Define categories for our data
categories = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']

print(f"Generating {num_rows} rows of sample data...")

# Open the file for writing
with open(file_name, 'w', newline='') as csvfile:
    # Create a CSV writer object
    writer = csv.writer(csvfile)

    # Write the header row
    writer.writerow(['category', 'name', 'text', 'address', 'number'])

    # Loop to generate and write each row
    for i in range(num_rows):
        category = random.choice(categories)
        value = random.uniform(1.0, 1000.0)
        writer.writerow([category, faker.name(), faker.text(), faker.address(), faker.json()])
        # Print progress every 500k rows
        if (i + 1) % 500000 == 0:
            print(f"  ...wrote {i + 1} rows")

print(f"'{file_name}' created successfully!")